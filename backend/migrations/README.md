# Ozgaar Database Initialization Scripts

This directory contains the SQL scripts needed to initialize the Ozgaar database on Supabase.

## Scripts Overview

1. **`final_supabase_init.sql`** - Main database schema with users, personas, jobs, applications, reviews, and core functionality
2. **`skill_categories_init.sql`** - Skill categories and subcategories master data
3. **`job_visibility_performance_init.sql`** - Job visibility and performance tracking features
4. **`complete_final_init.sql`** - Complete initialization script combining all features

## Usage Instructions

### Option 1: Complete Initialization (Recommended)
Run `complete_final_init.sql` in your Supabase SQL Editor to set up the entire database at once.

### Option 2: Modular Initialization
If you prefer to initialize the database in steps:
1. Run `final_supabase_init.sql` first to set up the core schema
2. Run `skill_categories_init.sql` to add skill categories
3. Run `job_visibility_performance_init.sql` to add job visibility and performance features

## Features Included

- **User Management**: Support for switchable worker/poster roles
- **Worker Personas**: Multiple skill-based profiles per user
- **Job Postings**: Full job posting and management system
- **Applications**: Job application tracking
- **Reviews**: Bidirectional review system
- **Skill Categories**: Comprehensive skill categorization for the Indian job market
- **Job Visibility**: Advanced job search and visibility features
- **Performance Tracking**: Analytics for job views, applications, and metrics
- **Authentication Flow**: Secure user registration and profile completion
- **Notifications**: In-app notification system
- **SMS & OTP**: SMS delivery tracking and OTP rate limiting

## Post-Initialization Steps

1. Configure your backend server with Supabase credentials
2. Test the complete authentication flow:
   - Phone registration → OTP verification → Profile completion
3. Verify skill categories are properly populated
4. Test job search functionality with `search_jobs_by_location` function

## Troubleshooting

If you encounter any issues during initialization:
1. Ensure all required extensions (uuid-ossp, postgis) are enabled
2. Check that you're running the scripts in the correct order if using modular initialization
3. Verify that your Supabase project has sufficient permissions for all operations