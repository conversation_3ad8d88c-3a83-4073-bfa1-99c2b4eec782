# Ozgaar Backend Server

Node.js backend server for the Ozgaar job platform, serving as an intermediary layer between the mobile app and Supabase.

## Architecture

This backend server provides:
- **Secure API endpoints** for mobile app authentication and data access
- **Supabase integration** with service role key for database operations
- **Twilio SMS integration** for OTP delivery with rate limiting
- **JWT authentication** for secure session management
- **Rate limiting and security** middleware for API protection
- **Comprehensive logging** and error handling

## Tech Stack

- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with security middleware
- **Database**: Supabase (PostgreSQL) with service role access
- **Authentication**: JWT tokens with refresh token support
- **SMS Service**: Twilio for OTP delivery
- **Logging**: <PERSON> with file and console outputs
- **Validation**: Joi for request validation
- **Security**: Helmet, CORS, rate limiting, input sanitization

## Project Structure

```
backend/
├── src/
│   ├── config/
│   │   ├── index.ts              # Environment configuration
│   │   └── database.ts           # Supabase client and database operations
│   ├── controllers/
│   │   └── authController.ts     # Authentication endpoints logic
│   ├── middleware/
│   │   ├── auth.ts               # JWT authentication middleware
│   │   ├── rateLimiter.ts        # Rate limiting middleware
│   │   ├── validation.ts         # Request validation middleware
│   │   └── errorHandler.ts       # Global error handling
│   ├── routes/
│   │   ├── index.ts              # Main router
│   │   └── auth.ts               # Authentication routes
│   ├── services/
│   │   └── 2factorService.ts      # Twilio SMS integration
│   ├── utils/
│   │   ├── jwt.ts                # JWT token utilities
│   │   ├── logger.ts             # Winston logger configuration
│   │   └── validation.ts         # Validation schemas and helpers
│   ├── app.ts                    # Express app configuration
│   └── index.ts                  # Server entry point
├── logs/                         # Log files (auto-created)
├── package.json
├── tsconfig.json
└── .env.example
```

## Setup Instructions

### 1. Install Dependencies

```bash
cd backend
npm install
```

### 2. Environment Configuration

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Supabase Configuration (Server-side only)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# 2Factor.in SMS Configuration (Primary Provider)
TWOFACTOR_API_KEY=your-2factor-api-key-here

# Legacy Twilio SMS Configuration (Deprecated)
TWILIO_ACCOUNT_SID=your-2factor-account-sid
TWILIO_AUTH_TOKEN=your-2factor-auth-token
TWILIO_PHONE_NUMBER=+**********
```

### 3. Start Development Server

```bash
npm run dev
```

The server will start on `http://localhost:3000`

### 4. Production Build

```bash
npm run build
npm start
```

## API Endpoints

### Authentication

#### POST `/api/auth/send-otp`
Send OTP to phone number for authentication.

**Request:**
```json
{
  "phone": "+************"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "phone": "+************",
    "expiresIn": 300,
    "remaining_attempts": 3
  }
}
```

#### POST `/api/auth/verify-otp`
Verify OTP and authenticate user.

**Request:**
```json
{
  "phone": "+************",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "user": {
      "id": "uuid",
      "phone": "+************",
      "full_name": "",
      "preferred_language": "hindi",
      "user_type": "worker",
      "is_verified": false,
      "profile_complete": false
    },
    "tokens": {
      "accessToken": "jwt-token",
      "refreshToken": "refresh-token",
      "expiresIn": "7d"
    }
  }
}
```

#### POST `/api/auth/refresh-token`
Refresh access token using refresh token.

#### GET `/api/auth/profile`
Get current user profile (requires authentication).

#### PUT `/api/auth/profile`
Update user profile (requires authentication).

### Health Check

#### GET `/health`
Check server and database health.

## Security Features

### Rate Limiting
- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 10 requests per 15 minutes per IP
- **OTP requests**: 5 requests per hour per phone/IP
- **Phone-specific**: 3 OTP attempts per hour per phone number

### Authentication
- **JWT tokens** with configurable expiration
- **Refresh token** rotation for enhanced security
- **Automatic token cleanup** on logout
- **Session validation** on protected routes

### Input Validation
- **Joi schemas** for all request validation
- **Phone number validation** for Indian mobile numbers
- **Input sanitization** to prevent XSS attacks
- **Type-safe** request/response handling

### Security Headers
- **Helmet.js** for security headers
- **CORS** configuration for cross-origin requests
- **Compression** for response optimization
- **Request size limits** to prevent abuse

## Monitoring and Logging

### Winston Logging
- **Console output** for development
- **File logging** for production (`logs/combined.log`, `logs/error.log`)
- **Structured logging** with timestamps and levels
- **HTTP request logging** with Morgan

### Error Handling
- **Global error handler** with proper HTTP status codes
- **Async error wrapper** for route handlers
- **Graceful shutdown** handling
- **Unhandled rejection** and exception catching

## Development

### Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm start           # Start production server
npm run lint        # Run ESLint
npm run lint:fix    # Fix ESLint issues
npm run format      # Format code with Prettier
npm test           # Run tests
npm run type-check # TypeScript type checking
```

### Environment Variables

See `.env.example` for all available configuration options.

### Database Operations

The server uses Supabase service role key for all database operations, ensuring:
- **Row Level Security** enforcement
- **Secure data access** without exposing credentials to mobile app
- **Centralized business logic** for data validation and processing
- **Audit logging** of all database operations

## Deployment

### Production Considerations

1. **Environment Variables**: Set all required environment variables
2. **SSL/TLS**: Use HTTPS in production
3. **Process Management**: Use PM2 or similar for process management
4. **Monitoring**: Set up application monitoring (e.g., New Relic, DataDog)
5. **Logging**: Configure log rotation and centralized logging
6. **Database**: Ensure Supabase is properly configured for production
7. **SMS**: Monitor Twilio usage and costs

### Docker Support

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

## Contributing

1. Follow TypeScript best practices
2. Add proper error handling for all operations
3. Include comprehensive logging
4. Write tests for new features
5. Update documentation for API changes
