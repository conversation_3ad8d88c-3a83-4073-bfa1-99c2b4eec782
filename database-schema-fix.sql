-- =====================================================
-- DATABASE SCHEMA FIX FOR AUTHENTICATION FLOW
-- =====================================================
-- Run this in your Supabase Cloud SQL Editor to fix the authentication flow issues
-- This makes full_name nullable initially and adds profile completion tracking
-- =====================================================

-- 1. Make full_name nullable to allow user creation without complete profile
ALTER TABLE users ALTER COLUMN full_name DROP NOT NULL;

-- 2. Add a profile_completed column to track profile completion status
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_completed BOOLEAN DEFAULT FALSE;

-- 3. Add a constraint to ensure profile is complete when full_name is provided
ALTER TABLE users ADD CONSTRAINT check_profile_completion 
  CHECK (
    (full_name IS NULL AND profile_completed = FALSE) OR 
    (full_name IS NOT NULL AND LENGTH(TRIM(full_name)) >= 2 AND profile_completed = TRUE)
  );

-- 4. Create an index for profile completion queries
CREATE INDEX IF NOT EXISTS idx_users_profile_completed ON users (profile_completed, created_at);

-- 5. Update the RLS policies to handle incomplete profiles
-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can insert own profile" ON users;

-- Create new policies that handle incomplete profiles
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 6. Create a function to complete user profile
CREATE OR REPLACE FUNCTION complete_user_profile(
  user_id UUID,
  full_name_param TEXT,
  preferred_language_param supported_language_enum DEFAULT 'hindi',
  user_type_param user_role_enum DEFAULT 'worker',
  email_param TEXT DEFAULT NULL,
  address_param TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  updated_user users%ROWTYPE;
BEGIN
  -- Validate full_name
  IF full_name_param IS NULL OR LENGTH(TRIM(full_name_param)) < 2 THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Full name must be at least 2 characters long'
    );
  END IF;

  -- Update user profile
  UPDATE users 
  SET 
    full_name = TRIM(full_name_param),
    preferred_language = preferred_language_param,
    user_type = user_type_param,
    email = email_param,
    address = address_param,
    profile_completed = TRUE,
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'User not found'
    );
  END IF;

  RETURN jsonb_build_object(
    'success', true,
    'user', row_to_json(updated_user)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create a function to check if user profile is complete
CREATE OR REPLACE FUNCTION is_profile_complete(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_record users%ROWTYPE;
BEGIN
  SELECT * INTO user_record FROM users WHERE id = user_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN (
    user_record.profile_completed = TRUE AND
    user_record.full_name IS NOT NULL AND
    LENGTH(TRIM(user_record.full_name)) >= 2
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Update existing users to have profile_completed = TRUE if they have full_name
UPDATE users 
SET profile_completed = TRUE 
WHERE full_name IS NOT NULL AND LENGTH(TRIM(full_name)) >= 2;

-- =====================================================
-- SCHEMA FIX COMPLETE
-- =====================================================
-- Changes made:
-- 1. full_name is now nullable for initial user creation
-- 2. Added profile_completed column to track completion status
-- 3. Added constraint to ensure data consistency
-- 4. Created helper functions for profile completion
-- 5. Updated existing complete profiles
-- =====================================================
