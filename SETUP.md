# Ozgaar Setup Guide

This guide will help you set up the complete Ozgaar job platform with Node.js backend server, Supabase Cloud database, and React Native mobile app.

## 🚀 **Quick Start (Recommended)**

**For the simplest setup experience, use Supabase Cloud:**

👉 **[Follow the Supabase Cloud Setup Guide](./SUPABASE-CLOUD-SETUP.md)** 👈

This eliminates local database complexity and gets you running in minutes.

## Architecture Overview

The platform uses a **3-tier architecture**:
1. **Mobile App** (React Native) - User interface
2. **Backend Server** (Node.js + Express) - API layer and business logic
3. **Database** (Supabase Cloud) - Hosted PostgreSQL database

The mobile app communicates only with the Node.js backend, which handles all Supabase interactions securely.

## Prerequisites

- Node.js 18+ and npm
- React Native development environment (Android Studio/Xcode)
- Supabase account
- Twilio account (for SMS backup)

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd ozgaar-android

# Install backend server dependencies
cd backend
npm install

# Install mobile dependencies
cd ../mobile
npm install

# Note: db-migrations directory doesn't need npm install
# It only contains Supabase CLI configuration and SQL migrations
```

### 2. Set Up Supabase Backend

#### Create Supabase Project
1. Go to [supabase.com](https://supabase.com) and create a new project
2. Note down your project URL and API keys from Settings > API

#### Configure Database Environment
```bash
cd db-migrations
cp .env.example .env
# Edit .env with your Supabase credentials
```

#### Run Database Setup Script
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

This will:
- Install Supabase CLI (if not already installed)
- Initialize the project
- Apply database migrations
- Start local Supabase database

### 3. Configure Twilio SMS (Required)

#### Get Twilio Credentials
1. Sign up at [2factor.com](https://2factor.com)
2. Get a phone number capable of sending SMS to India (+91)
3. Note down your Account SID, Auth Token, and Phone Number

#### Add to Server Environment
```bash
# In server/.env
TWOFACTOR_API_KEY=your-2factor-api-key-here

# Legacy Twilio (deprecated)
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_PHONE_NUMBER=+**********
```

### 4. Configure Mobile App

#### Set Up Environment
```bash
cd mobile
cp .env.example .env
# Edit .env with your Supabase URL and keys
```

#### Configure Backend Server
```bash
cd backend
cp .env.example .env
# Edit backend/.env with your Supabase and 2Factor.in credentials
```

#### Configure Mobile App
```bash
# In mobile/.env
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000/api

# For production:
# EXPO_PUBLIC_API_BASE_URL=https://your-backend-domain.com/api
```

### 5. Start Development

#### Terminal 1: Start Database
```bash
cd db-migrations
npm run dev
```

#### Terminal 2: Start Backend Server
```bash
cd backend
npm run dev
```

#### Terminal 3: Start Mobile App
```bash
cd mobile
npm start
```

## 📱 Testing User Story 1.1

The phone registration flow includes:

### ✅ Features Implemented
- **Phone Input**: +91 country code locked, 10-digit validation
- **Real-time Formatting**: Displays as 98765-43210 format
- **OTP Delivery**: Primary via Supabase Auth, backup via Twilio
- **OTP Verification**: 6-digit code with 3 attempts, 5-minute expiry
- **Rate Limiting**: 3 OTP attempts per phone per hour
- **Language Selection**: 8 Indian languages with regional detection
- **Error Handling**: Clear messages with retry options
- **Analytics Ready**: SMS delivery tracking and user registration metrics

### 🧪 Test Scenarios

1. **Happy Path**:
   - Enter valid Indian mobile number (e.g., 9876543210)
   - Receive OTP via SMS
   - Enter correct OTP
   - Select preferred language
   - Navigate to main app

2. **Error Scenarios**:
   - Invalid phone number format
   - OTP delivery failure (automatic Twilio backup)
   - Wrong OTP entry (3 attempts)
   - Rate limiting after multiple attempts

3. **Regional Features**:
   - Language auto-detection based on area code
   - Proper formatting for Indian numbers
   - SMS delivery optimization for Indian carriers

## 🔧 Development URLs

When running locally:
- **Supabase Studio**: http://localhost:54323
- **API**: http://localhost:54321
- **Email Testing**: http://localhost:54324

## 📊 Database Schema

The database includes:
- **users**: Base user profiles with phone authentication
- **worker_personas**: Multi-persona worker profiles
- **jobs**: Job postings with location-based matching
- **otp_attempts**: Rate limiting for SMS delivery
- **sms_logs**: SMS delivery analytics

## 🚨 Important Notes

1. **SMS Costs**: 2Factor.in charges per SMS. Monitor usage in production.

2. **Rate Limiting**: Implemented to prevent SMS abuse:
   - 3 attempts per phone per hour
   - 1-hour block after exceeding limit

3. **Security**: 
   - Row Level Security (RLS) enabled on all tables
   - JWT-based authentication
   - Phone number validation on both client and server

4. **Regional Optimization**:
   - +91 country code locked for Indian market
   - Area code to language mapping
   - SMS delivery optimized for Indian carriers

## 🔄 Next Steps

After completing User Story 1.1, you can proceed with:
- User Story 1.2: Smart Language Selection (already implemented)
- Epic 2: Worker Profile & Persona System
- Epic 3: Job Posting & Management

## 🐛 Troubleshooting

### Common Issues

1. **OTP Not Received**:
   - Check Twilio phone number configuration
   - Verify Indian mobile number format
   - Check SMS logs in Supabase Studio

2. **Database Connection Issues**:
   - Ensure Supabase is running: `supabase status`
   - Check environment variables
   - Verify database migrations: `supabase db push`

3. **Mobile App Issues**:
   - Clear Metro cache: `npx expo start --clear`
   - Check environment variables in .env
   - Verify Supabase URL accessibility

### Getting Help

- Check server logs: `npm run logs`
- View Supabase logs in Studio
- Check mobile app logs in Expo Dev Tools
