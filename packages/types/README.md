# Rozgaar Types Package Documentation

## Overview

This package contains shared TypeScript types used across both the mobile and backend applications in the Rozgaar project. Centralizing types in this package helps maintain consistency and reduces duplication between applications.

## Package Structure

```
packages/types/
├── src/
│   └── index.ts        # Main entry point exporting all shared types
├── package.json        # Package manifest
└── tsconfig.json       # TypeScript configuration
```

## Usage

### Installation

The package is automatically available to all workspace packages through the monorepo setup. No additional installation is required.

### Importing Types

To use types from this package, import them directly:

```typescript
// In mobile or backend code
import { User, SkillCategoryData } from '@ozgaar/types';

const user: User = {
  // ... user properties
};
```

## Adding New Types

1. Add the new type definitions to `packages/types/src/index.ts`
2. Export the types from the main entry point
3. Use the types in mobile and backend applications by importing from `@ozgaar/types`

## Best Practices

1. **Keep types platform-agnostic**: Avoid Node.js or React Native specific types in the shared package
2. **Export all types**: Ensure all types that need to be shared are exported from the main entry point
3. **Maintain backward compatibility**: When modifying existing types, ensure changes don't break existing code
4. **Document complex types**: Add comments for types that might be unclear to other developers

## Type Categories

The shared types are organized into several categories:

1. **Base Types**: Fundamental types like `SupportedLanguage`
2. **Skill Types**: All types related to skill categories and subcategories
3. **User Types**: Types related to user profiles and authentication
4. **Job Types**: Types related to job postings and applications
5. **Worker Persona Types**: Types related to worker personas and availability
6. **Trust Score Types**: Types related to user trust and verification

## Migration from Local Types

When migrating from locally defined types to the shared package:

1. Remove the local type definitions
2. Update imports to use `@ozgaar/types`
3. Verify that all functionality still works as expected
4. Update any references to renamed or modified types

## Troubleshooting

If you encounter issues with type resolution:

1. Ensure the package is properly referenced in `tsconfig.json`
2. Check that the monorepo workspace is set up correctly
3. Restart your TypeScript server/IDE
4. Run `npm install` from the root directory to ensure all dependencies are linked