-- =====================================================
-- OZGAAR DATABASE SETUP FOR SUPABASE CLOUD
-- =====================================================
-- Run this script in your Supabase Cloud SQL Editor
-- This will create all tables, indexes, RLS policies, and functions
--
-- INCLUDES AUTHENTICATION FLOW FIXES:
-- - Nullable full_name for initial user creation
-- - Profile completion tracking system
-- - Helper functions for profile management
-- - Updated constraints and indexes
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- CUSTOM TYPES FOR INDIAN JOB MARKET
-- =====================================================

CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
CREATE TYPE supported_language_enum AS ENUM (
  'hindi', 'english', 'tamil', 'telugu', 'bengali', 
  'marathi', 'gujarati', 'kannada'
);
CREATE TYPE skill_category_enum AS ENUM (
  'electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning',
  'driving', 'delivery', 'security', 'gardening', 'tutoring'
);
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE job_status_enum AS ENUM ('active', 'paused', 'filled', 'cancelled', 'expired');
CREATE TYPE application_status_enum AS ENUM ('pending', 'viewed', 'shortlisted', 'accepted', 'rejected', 'withdrawn');
CREATE TYPE review_type_enum AS ENUM ('worker_review', 'poster_review');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');

-- =====================================================
-- MAIN TABLES
-- =====================================================

-- Users table (base entity for all platform users)
-- UPDATED FOR AUTHENTICATION FLOW: full_name is nullable for initial user creation
-- ENHANCED FOR WORKER PROFILE: Added worker profile fields at user level
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) UNIQUE NOT NULL, -- +91 format for India
    email VARCHAR(255),
    full_name VARCHAR(100), -- NULLABLE: Allows user creation without complete profile
    preferred_language supported_language_enum DEFAULT 'english',
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for lat/lng
    address TEXT,
    user_type user_role_enum DEFAULT 'worker',
    is_verified BOOLEAN DEFAULT FALSE,
    profile_completed BOOLEAN DEFAULT FALSE, -- NEW: Tracks profile completion status
    profile_image_url TEXT,

    -- Worker profile fields at user level
    primary_skill_category skill_category_enum, -- Primary skill for basic categorization
    currently_available BOOLEAN DEFAULT TRUE, -- Quick availability status
    years_of_experience INTEGER DEFAULT 0 CHECK (years_of_experience >= 0), -- Overall experience

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$'),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$'),
    -- UPDATED: Ensures profile consistency - workers must have primary_skill_category when complete
    CONSTRAINT check_profile_completion CHECK (
        (full_name IS NULL AND profile_completed = FALSE) OR
        (full_name IS NOT NULL AND LENGTH(TRIM(full_name)) >= 2 AND profile_completed = TRUE AND
         (user_type != 'worker' OR primary_skill_category IS NOT NULL))
    )
);

-- Performance indexes for user worker profile queries
CREATE INDEX IF NOT EXISTS idx_users_primary_skill_category ON users(primary_skill_category) WHERE primary_skill_category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_currently_available ON users(currently_available) WHERE currently_available = TRUE;
CREATE INDEX IF NOT EXISTS idx_users_worker_profile ON users(user_type, primary_skill_category, currently_available) WHERE user_type IN ('worker', 'both');

-- Worker personas table (core multi-persona feature)
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician"
    skill_category skill_category_enum NOT NULL,
    skill_subcategories TEXT[], -- Array of related skills
    description TEXT,
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    
    -- Pricing structure for Indian market
    hourly_rate DECIMAL(8,2) CHECK (hourly_rate >= 0),
    daily_rate DECIMAL(8,2) CHECK (daily_rate >= 0),
    monthly_rate DECIMAL(10,2) CHECK (monthly_rate >= 0),
    is_rate_negotiable BOOLEAN DEFAULT TRUE,
    
    -- Availability and location
    availability_pattern JSONB NOT NULL DEFAULT '{}', -- Weekly schedule
    travel_radius_km INTEGER DEFAULT 10 CHECK (travel_radius_km > 0),
    
    -- Performance metrics
    total_jobs_completed INTEGER DEFAULT 0 CHECK (total_jobs_completed >= 0),
    total_earnings DECIMAL(12,2) DEFAULT 0 CHECK (total_earnings >= 0),
    average_rating DECIMAL(3,2) DEFAULT 0 CHECK (average_rating >= 0 AND average_rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (
      average_rating = 0 OR (total_reviews > 0 AND average_rating > 0)
    )
);

-- Jobs table (job postings from employers)
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poster_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    skill_category skill_category_enum NOT NULL,
    
    -- Location for hyper-local matching
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    address TEXT NOT NULL,
    landmark TEXT,
    
    -- Job specifications
    job_type job_type_enum DEFAULT 'one_time',
    urgency urgency_enum DEFAULT 'normal',
    budget_min DECIMAL(10,2) NOT NULL CHECK (budget_min > 0),
    budget_max DECIMAL(10,2) NOT NULL CHECK (budget_max >= budget_min),
    estimated_duration_hours INTEGER CHECK (estimated_duration_hours > 0),
    
    -- Requirements and preferences
    requirements TEXT,
    preferred_gender gender_preference_enum DEFAULT 'any',
    min_experience_years INTEGER DEFAULT 0 CHECK (min_experience_years >= 0),
    min_rating DECIMAL(3,2) DEFAULT 0 CHECK (min_rating >= 0 AND min_rating <= 5),
    
    -- Status and metrics
    status job_status_enum DEFAULT 'active',
    applications_count INTEGER DEFAULT 0 CHECK (applications_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    
    -- Timestamps
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_expiry CHECK (expires_at > created_at)
);

-- Job applications (connects personas to jobs)
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_persona_id UUID NOT NULL REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Application details
    status application_status_enum DEFAULT 'pending',
    proposed_rate DECIMAL(8,2),
    message TEXT,
    
    -- Response tracking
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    poster_response TEXT,
    
    -- Prevent duplicate applications
    UNIQUE(job_id, worker_persona_id)
);

-- Reviews table (bidirectional trust system)
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    worker_persona_id UUID REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_type review_type_enum NOT NULL,
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reviews
    UNIQUE(job_id, reviewer_id, review_type),
    
    -- Ensure reviewee consistency
    CONSTRAINT valid_reviewee CHECK (
      (review_type = 'worker_review' AND worker_persona_id IS NOT NULL) OR
      (review_type = 'poster_review' AND worker_persona_id IS NULL)
    )
);

-- Notifications table for in-app, push, email, and SMS notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'job_application_received', 'application_status_updated', etc.
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB, -- Additional data for deep linking, etc.
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- OTP attempts tracking table for rate limiting
CREATE TABLE otp_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) NOT NULL,
    attempts_count INTEGER DEFAULT 1,
    last_attempt_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$')
);

-- SMS logs table for tracking SMS delivery and analytics
CREATE TABLE sms_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) NOT NULL,
    session_id VARCHAR(100), -- 2Factor.in session ID for OTP verification
    status VARCHAR(20) NOT NULL, -- sent, delivered, failed, etc.
    provider VARCHAR(20) NOT NULL DEFAULT '2factor',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$')
);

-- =====================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- =====================================================

CREATE INDEX idx_users_location ON users USING GIST (location);
CREATE INDEX idx_jobs_location ON jobs USING GIST (location);
CREATE INDEX idx_jobs_active_category ON jobs (skill_category, status, created_at) WHERE status = 'active';
CREATE INDEX idx_personas_active_category ON worker_personas (skill_category, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_applications_job ON job_applications (job_id, status);
CREATE INDEX idx_reviews_reviewee_persona ON reviews (reviewee_id, worker_persona_id, rating);
CREATE INDEX idx_otp_attempts_phone ON otp_attempts (phone, last_attempt_at);
CREATE INDEX idx_sms_logs_phone_status ON sms_logs (phone, status, created_at);
CREATE INDEX idx_sms_logs_created_at ON sms_logs (created_at);
-- NEW: Index for profile completion queries (authentication flow optimization)
CREATE INDEX idx_users_profile_completed ON users (profile_completed, created_at);
-- NEW: Indexes for notifications
CREATE INDEX idx_notifications_user_id ON notifications (user_id, created_at DESC);
CREATE INDEX idx_notifications_user_read ON notifications (user_id, read, created_at DESC);

-- =====================================================
-- ROW LEVEL SECURITY SETUP
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE worker_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE otp_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE sms_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Worker personas policies
CREATE POLICY "Users can manage own personas" ON worker_personas
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Public can view active personas" ON worker_personas
  FOR SELECT USING (is_active = TRUE);

-- Jobs table policies
CREATE POLICY "Public can view active jobs" ON jobs
  FOR SELECT USING (status = 'active');

CREATE POLICY "Users can manage own job postings" ON jobs
  FOR ALL USING (poster_id = auth.uid());

-- Job applications policies
CREATE POLICY "Users can view applications for their jobs" ON job_applications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM jobs
      WHERE jobs.id = job_applications.job_id
      AND jobs.poster_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own applications" ON job_applications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create applications for their personas" ON job_applications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own applications" ON job_applications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

-- Reviews table policies
CREATE POLICY "Users can view reviews for their personas" ON reviews
  FOR SELECT USING (
    reviewer_id = auth.uid() OR
    reviewee_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = reviews.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create reviews" ON reviews
  FOR INSERT WITH CHECK (reviewer_id = auth.uid());

-- OTP attempts policies (service role only)
CREATE POLICY "Service role can manage OTP attempts" ON otp_attempts
  FOR ALL USING (auth.role() = 'service_role');

-- SMS logs policies (service role only)
CREATE POLICY "Service role can manage SMS logs" ON sms_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Notifications table policies
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage own notifications" ON notifications
  FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- DATABASE FUNCTIONS AND TRIGGERS
-- =====================================================

-- Auto-update triggers for ratings and counters
CREATE OR REPLACE FUNCTION update_persona_rating()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.worker_persona_id IS NOT NULL THEN
    UPDATE worker_personas
    SET
      average_rating = (
        SELECT ROUND(AVG(rating)::numeric, 2)
        FROM reviews
        WHERE worker_persona_id = NEW.worker_persona_id
        AND review_type = 'worker_review'
      ),
      total_reviews = (
        SELECT COUNT(*)
        FROM reviews
        WHERE worker_persona_id = NEW.worker_persona_id
        AND review_type = 'worker_review'
      ),
      updated_at = NOW()
    WHERE id = NEW.worker_persona_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_persona_rating
  AFTER INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_persona_rating();

-- Function to update job application counts
CREATE OR REPLACE FUNCTION update_job_application_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE jobs
    SET applications_count = applications_count + 1,
        updated_at = NOW()
    WHERE id = NEW.job_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE jobs
    SET applications_count = applications_count - 1,
        updated_at = NOW()
    WHERE id = OLD.job_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_job_application_count
  AFTER INSERT OR DELETE ON job_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_job_application_count();

-- Function to handle OTP rate limiting
CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_record RECORD;
  current_time TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get or create attempt record
  SELECT * INTO attempt_record
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF attempt_record.blocked_until IS NOT NULL AND attempt_record.blocked_until > current_time THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', attempt_record.blocked_until,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed
  IF attempt_record.last_attempt_at IS NULL OR
     (current_time - attempt_record.last_attempt_at) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_time)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = current_time,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_record.attempts_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_time + block_duration,
        last_attempt_at = current_time
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_time + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = attempts_count + 1,
      last_attempt_at = current_time
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (attempt_record.attempts_count + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get SMS delivery statistics
CREATE OR REPLACE FUNCTION get_sms_stats(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS JSONB AS $$
DECLARE
  stats JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_sent', COUNT(*),
    'delivered', COUNT(*) FILTER (WHERE status = 'delivered'),
    'failed', COUNT(*) FILTER (WHERE status = 'failed'),
    'pending', COUNT(*) FILTER (WHERE status = 'sent'),
    'delivery_rate',
      CASE
        WHEN COUNT(*) > 0 THEN
          ROUND((COUNT(*) FILTER (WHERE status = 'delivered')::numeric / COUNT(*)::numeric) * 100, 2)
        ELSE 0
      END,
    'by_hour', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'hour', date_trunc('hour', created_at),
          'count', count
        ) ORDER BY hour
      )
      FROM (
        SELECT
          date_trunc('hour', created_at) as hour,
          COUNT(*) as count
        FROM sms_logs
        WHERE created_at BETWEEN start_date AND end_date
        GROUP BY date_trunc('hour', created_at)
      ) hourly_stats
    )
  ) INTO stats
  FROM sms_logs
  WHERE created_at BETWEEN start_date AND end_date;

  RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- AUTHENTICATION FLOW HELPER FUNCTIONS
-- =====================================================

-- Function to complete user profile (used by authentication flow)
CREATE OR REPLACE FUNCTION complete_user_profile(
  user_id UUID,
  full_name_param TEXT,
  preferred_language_param supported_language_enum DEFAULT 'english',
  user_type_param user_role_enum DEFAULT 'worker',
  email_param TEXT DEFAULT NULL,
  address_param TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  updated_user users%ROWTYPE;
BEGIN
  -- Validate full_name
  IF full_name_param IS NULL OR LENGTH(TRIM(full_name_param)) < 2 THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Full name must be at least 2 characters long'
    );
  END IF;

  -- Update user profile
  UPDATE users
  SET
    full_name = TRIM(full_name_param),
    preferred_language = preferred_language_param,
    user_type = user_type_param,
    email = email_param,
    address = address_param,
    profile_completed = TRUE,
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'User not found'
    );
  END IF;

  RETURN jsonb_build_object(
    'success', true,
    'user', row_to_json(updated_user)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user profile is complete
CREATE OR REPLACE FUNCTION is_profile_complete(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_record users%ROWTYPE;
BEGIN
  SELECT * INTO user_record FROM users WHERE id = user_id;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  RETURN (
    user_record.profile_completed = TRUE AND
    user_record.full_name IS NOT NULL AND
    LENGTH(TRIM(user_record.full_name)) >= 2
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Your Ozgaar database is now ready with authentication flow fixes!
--
-- AUTHENTICATION FLOW FEATURES:
-- ✅ Users can be created with minimal data (phone only)
-- ✅ Profile completion is tracked with profile_completed column
-- ✅ Helper functions for profile management
-- ✅ Proper constraints ensure data consistency
-- ✅ Optimized indexes for profile completion queries
--
-- Next steps:
-- 1. Configure your backend server with Supabase Cloud credentials
-- 2. Test the complete authentication flow:
--    - Phone registration → OTP verification → Profile completion
-- 3. Start building your application features
-- =====================================================

-- Fixes

-- Drop and recreate the function with fixed variable naming
DROP FUNCTION IF EXISTS handle_otp_rate_limit(TEXT);

CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_count INTEGER;
  last_attempt TIMESTAMP WITH TIME ZONE;
  blocked_until_time TIMESTAMP WITH TIME ZONE;
  current_timestamp TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get attempt record with explicit column selection
  SELECT attempts_count, last_attempt_at, blocked_until
  INTO attempt_count, last_attempt, blocked_until_time
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF blocked_until_time IS NOT NULL AND blocked_until_time > current_timestamp THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', blocked_until_time,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed or no record exists
  IF attempt_count IS NULL OR
     last_attempt IS NULL OR
     (current_timestamp - last_attempt) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_timestamp)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = current_timestamp,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_timestamp + block_duration,
        last_attempt_at = current_timestamp
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_timestamp + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = COALESCE(attempt_count, 0) + 1,
      last_attempt_at = current_timestamp
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (COALESCE(attempt_count, 0) + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add unique constraint to phone column for ON CONFLICT to work
ALTER TABLE otp_attempts ADD CONSTRAINT otp_attempts_phone_unique UNIQUE (phone);