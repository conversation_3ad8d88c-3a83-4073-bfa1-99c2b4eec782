import React, { useEffect } from "react";
import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import { router } from 'expo-router';
import { useAuth } from '../hooks/useAuth';

const Index = () => {
  const { session, user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (session && user && user.profile_completed) {
        // User is authenticated and has complete profile, navigate to main app
        router.replace('/(tabs)');
      } else if (session && user && !user.profile_completed) {
        // User is authenticated but profile incomplete, navigate to profile completion
        if (user.user_type === 'worker') router.replace('/(auth)/worker-profile-creation');
        else router.replace('/(auth)/profile-completion');
      } else if (session && !user) {
        // User is authenticated but no profile data, navigate to profile completion
        router.replace('/(auth)/user-type-selection');
      } else {
        // User is not authenticated, navigate to phone registration
        router.replace('/(auth)/phone-registration');
      }
    }
  }, [session, user, loading]);

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#059669" />
        <Text style={styles.loaderText}>Ozgaar</Text>
        <Text style={styles.loaderSubtext}>Trust-first job platform</Text>
      </View>
    );
  }

  // This should not be reached as we navigate away
  return null;
};

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#059669',
    marginTop: 16,
  },
  loaderSubtext: {
    color: '#6B7280',
  },
});

export default Index;
