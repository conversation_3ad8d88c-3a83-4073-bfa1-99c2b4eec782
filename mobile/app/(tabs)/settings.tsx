import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from "react-native";
import {LocalizedText} from "../../components/LocalizedText";
import { authHelpers } from "../../lib/supabase";
import { languageSyncService } from "../../lib/languageSync";
import { router } from 'expo-router';
import { useAppSelector } from '../../hooks/redux';
import { useTranslation } from 'react-i18next';
import { RootState } from "@/store";

const SettingsTab = () => {
    const { t } = useTranslation();
    const translationState = useAppSelector(state => state.translation);
    const { 
        availableLanguages, 
        currentLanguage, 
        isLoading: translationLoading 
    } = translationState;

    const [isChangingLanguage, setIsChangingLanguage] = useState(false);
    const [currentUser, setCurrentUser] = useState<any>(null);
    const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
    const dispatch = useAppDispatch();
    const { user } = useAppSelector((state: RootState) => state.user);

    useEffect(() => {
        // Get current user and last sync time
        const loadUserData = async () => {
            const { session } = await authHelpers.getSession();
            if (session?.user) {
                setCurrentUser(session.user);
            }

            const syncTime = await languageSyncService.getLastSyncTime();
            setLastSyncTime(syncTime);
        };

        loadUserData();
    }, []);

    const handleLanguageChange = async (languageCode: string) => {
        if (languageCode === currentLanguage) return;

        setIsChangingLanguage(true);

        try {
            // In a real implementation, we would dispatch an action to change the language
            // For now, we'll just show an alert
            Alert.alert(
                t('settings.languageChanged'),
                t('auth.languageSelection.languageSetMessage', {
                    language: availableLanguages.find(l => l.code === languageCode)?.name || languageCode
                })
            );

            // Update last sync time
            const newSyncTime = await languageSyncService.getLastSyncTime();
            setLastSyncTime(newSyncTime);

        } catch (error) {
            console.error('Failed to change language:', error);
            Alert.alert(t('common.error'), t('errors.settingsSaveError'));
        } finally {
            setIsChangingLanguage(false);
        }
    };

    const formatSyncTime = (timestamp: number | null) => {
        if (!timestamp) return t('settings.neverSynced');

        const date = new Date(timestamp);
        return date.toLocaleString();
    };

    return (
        <SafeAreaView style={styles.container}>
            <ScrollView style={styles.tabContent}>
                <View style={styles.pageHeader}>
                    <LocalizedText variant="title" style={styles.pageTitle}>
                        {t('settings.title')}
                    </LocalizedText>
                    <LocalizedText style={styles.pageSubtitle}>
                        {t('settings.subtitle')}
                    </LocalizedText>
                </View>

                <View style={styles.listContainer}>
                    {/* User Type Selection (Toggel between worker and poster) */}
                    <View style={styles.card}>
                        <TouchableOpacity
                            style={styles.settingItem}
                            onPress={handleUserSwitch}
                        >
                            <LocalizedText style={styles.settingText}>
                                {user.viewMode === 'worker' ? t('settings.switchToPoster') : t('settings.switchToWorker')}
                            </LocalizedText>
                        </TouchableOpacity>                        
                    </View>
                    {/* Language Preferences Section */}
                    <View style={styles.card}>
                        <LocalizedText variant="heading" style={styles.cardTitle}>
                            {t('settings.language')}
                        </LocalizedText>

                        <View style={styles.languageGrid}>
                            {availableLanguages.map((language) => (
                                <TouchableOpacity
                                    key={language.code}
                                    style={[
                                        styles.languageOption,
                                        currentLanguage === language.code && styles.languageOptionSelected
                                    ]}
                                    onPress={() => handleLanguageChange(language.code)}
                                    disabled={isChangingLanguage || translationLoading}
                                >
                                    <Text style={styles.languageFlag}>{language.flag}</Text>
                                    <LocalizedText
                                        languageCode={language.code}
                                        style={[
                                            styles.languageNative,
                                            currentLanguage === language.code && styles.languageNativeSelected
                                        ]}
                                    >
                                        {language.nativeName}
                                    </LocalizedText>
                                    <Text style={[
                                        styles.languageName,
                                        currentLanguage === language.code && styles.languageNameSelected
                                    ]}>
                                        {language.name}
                                    </Text>

                                    {language.hasVoiceInput && (
                                        <View style={styles.voiceIndicator}>
                                            <Text style={styles.voiceIcon}>🎤</Text>
                                        </View>
                                    )}

                                    {currentLanguage === language.code && (
                                        <View style={styles.selectedIndicator}>
                                            <Text style={styles.selectedIcon}>✓</Text>
                                        </View>
                                    )}
                                </TouchableOpacity>
                            ))}
                        </View>

                        {(isChangingLanguage || translationLoading) && (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator size="small" color="#3B82F6" />
                                <LocalizedText style={styles.loadingText}>
                                    {t('settings.changingLanguage')}
                                </LocalizedText>
                            </View>
                        )}

                        {lastSyncTime && (
                            <LocalizedText style={styles.syncInfo}>
                                {t('settings.lastSynced')}: {formatSyncTime(lastSyncTime)}
                            </LocalizedText>
                        )}
                    </View>

                    {/* Additional Settings Sections */}
                    <View style={styles.card}>
                        <LocalizedText variant="heading" style={styles.cardTitle}>
                            {t('settings.account')}
                        </LocalizedText>
                        <TouchableOpacity
                            style={styles.settingItem}
                            onPress={() => router.push('/edit-profile')}
                        >
                            <LocalizedText style={styles.settingText}>
                                Edit Profile
                            </LocalizedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.settingItem}>
                            <LocalizedText style={styles.settingText}>
                                {t('settings.privacy')}
                            </LocalizedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.settingItem}>
                            <LocalizedText style={styles.settingText}>
                                {t('settings.notifications')}
                            </LocalizedText>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.card}>
                        <LocalizedText variant="heading" style={styles.cardTitle}>
                            {t('settings.help')}
                        </LocalizedText>
                        <TouchableOpacity style={styles.settingItem}>
                            <LocalizedText style={styles.settingText}>
                                {t('settings.about')}
                            </LocalizedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={[styles.settingItem, styles.logoutItem]}>
                            <LocalizedText style={[styles.settingText, styles.logoutText]}>
                                {t('settings.logout')}
                            </LocalizedText>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F3F4F6'
    },
    tabContent: {
        flex: 1
    },
    pageHeader: {
        backgroundColor: 'white',
        padding: 24,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB'
    },
    pageTitle: {
        fontSize: 24,
        fontWeight: 'bold'
    },
    pageSubtitle: {
        color: '#6B7280',
        marginTop: 4
    },
    listContainer: {
        paddingHorizontal: 16,
        gap: 16,
        paddingBottom: 16,
        paddingTop: 16
    },
    card: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16
    },
    cardTitle: {
        fontWeight: '600',
        marginBottom: 16
    },
    languageGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginBottom: 16,
    },
    languageOption: {
        width: '48%',
        backgroundColor: '#F9FAFB',
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
        position: 'relative',
    },
    languageOptionSelected: {
        backgroundColor: '#EBF8FF',
        borderColor: '#3B82F6',
    },
    languageFlag: {
        fontSize: 24,
        marginBottom: 8,
    },
    languageNative: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 4,
    },
    languageNativeSelected: {
        color: '#1E40AF',
    },
    languageName: {
        fontSize: 12,
        color: '#6B7280',
        textAlign: 'center',
    },
    languageNameSelected: {
        color: '#3B82F6',
    },
    voiceIndicator: {
        position: 'absolute',
        top: 8,
        left: 8,
        backgroundColor: '#10B981',
        borderRadius: 10,
        width: 20,
        height: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    voiceIcon: {
        fontSize: 10,
    },
    selectedIndicator: {
        position: 'absolute',
        top: 8,
        right: 8,
        backgroundColor: '#3B82F6',
        borderRadius: 10,
        width: 20,
        height: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedIcon: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        gap: 8,
    },
    loadingText: {
        color: '#6B7280',
        fontSize: 14,
    },
    syncInfo: {
        fontSize: 12,
        color: '#9CA3AF',
        textAlign: 'center',
        marginTop: 8,
    },
    settingItem: {
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F3F4F6',
    },
    settingText: {
        fontSize: 16,
        color: '#1F2937',
    },
    logoutItem: {
        borderBottomWidth: 0,
    },
    logoutText: {
        color: '#EF4444',
    },
});

export default SettingsTab;
