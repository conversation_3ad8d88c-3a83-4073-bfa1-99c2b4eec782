# Ozgaar Supabase Cloud Setup Guide

This guide will help you set up Ozgaar with **Supabase Cloud** (hosted) instead of running a local database instance.

## 🎯 **Benefits of Cloud Setup**

- ✅ **No local database complexity** - No Docker, no local Supabase instance
- ✅ **Production-ready from day 1** - Same database for development and production
- ✅ **Automatic backups and scaling** - Managed by Supabase
- ✅ **Global CDN and performance** - Fast worldwide access
- ✅ **Built-in monitoring** - Database metrics and logs in Supabase dashboard

## 📋 **Prerequisites**

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **2Factor Account**: Sign up at [2Factor.in](https://2Factor.in) for SMS functionality
3. **Node.js 18+**: For running the backend server

## 🚀 **Step 1: Create Supabase Cloud Project**

### 1.1 Create New Project
1. Go to [supabase.com](https://supabase.com) and sign in
2. Click **"New Project"**
3. Choose your organization
4. Fill in project details:
   - **Name**: `ozgaar-production` (or your preferred name)
   - **Database Password**: Generate a strong password (save it!)
   - **Region**: Choose closest to your users (e.g., `ap-south-1` for India)
5. Click **"Create new project"**
6. Wait 2-3 minutes for project initialization

### 1.2 Get Project Credentials
1. Go to **Settings** → **API**
2. Copy these values (you'll need them later):
   - **Project URL** (e.g., `https://abcdefgh.supabase.co`)
   - **anon public** key
   - **service_role** key (⚠️ Keep this secret!)

## 🗄️ **Step 2: Set Up Database Schema**

### 2.1 Run Database Setup Script
1. In your Supabase project, go to **SQL Editor**
2. Click **"New Query"**
3. Copy the entire contents of `supabase-cloud-setup.sql` file
4. Paste it into the SQL Editor
5. Click **"Run"** (this may take 30-60 seconds)
6. You should see: **"Success. No rows returned"**

### 2.2 Verify Database Setup
1. Go to **Table Editor** in Supabase dashboard
2. You should see these tables:
   - `users`
   - `worker_personas`
   - `jobs`
   - `job_applications`
   - `reviews`
   - `otp_attempts`
   - `sms_logs`

## 📱 **Step 3: Configure 2Factor SMS**

### 3.1 Set Up 2Factor Account
1. Sign up at [2Factor.in](https://2Factor.in)
2. Complete phone verification
3. Go to **Console Dashboard**
4. Note down:
   - **Account SID**
   - **Auth Token**

### 3.2 Get Phone Number
1. Go to **Phone Numbers** → **Manage** → **Buy a number**
2. Choose a number that supports SMS
3. **Important**: For Indian users, you may need:
   - A US/UK number (works internationally)
   - Or register for India-specific messaging (requires business verification)
4. Note down your **2Factor Phone Number** (e.g., `+**********`)

### 3.3 Test 2Factor (Optional)
1. Go to **Console** → **Messaging** → **Try it out**
2. Send a test SMS to your phone to verify it works

## ⚙️ **Step 4: Configure Backend Server**

### 4.1 Update Backend Environment
```bash
cd backend
cp .env.example .env
```

### 4.2 Edit `.env` file with your credentials:
```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Supabase Cloud Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# 2Factor.in SMS Configuration (Primary Provider)
TWOFACTOR_API_KEY=your-2factor-api-key-here

# Legacy 2Factor SMS Configuration (Deprecated)
2Factor_ACCOUNT_SID=your-2Factor-account-sid
2Factor_AUTH_TOKEN=your-2Factor-auth-token
2Factor_PHONE_NUMBER=+**********

# Rate Limiting Configuration (optional - defaults are fine)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
OTP_RATE_LIMIT_ATTEMPTS=3
OTP_RATE_LIMIT_WINDOW_HOURS=1
```

### 4.3 Install Dependencies and Start Backend
```bash
cd backend
npm install
npm run dev
```

You should see:
```
🚀 Ozgaar Backend Server started successfully
📍 Server running on http://localhost:3000
🌍 Environment: development
📊 Health check: http://localhost:3000/health
```

## 📱 **Step 5: Configure Mobile App**

### 5.1 Update Mobile Environment
```bash
cd mobile
cp .env.example .env
```

### 5.2 Edit `mobile/.env`:
```env
# Backend API Configuration
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000/api

# For production deployment, use your deployed backend URL:
# EXPO_PUBLIC_API_BASE_URL=https://your-backend-domain.com/api

# App Configuration
EXPO_PUBLIC_ENV=development
EXPO_PUBLIC_APP_NAME=Ozgaar
```

### 5.3 Start Mobile App
```bash
cd mobile
npm install
npm start
```

## 🧪 **Step 6: Test the Complete Flow**

### 6.1 Test Backend Health
```bash
curl http://localhost:3000/health
```

Expected response:
```json
{
  "success": true,
  "message": "Ozgaar Backend API is healthy",
  "services": {
    "database": "healthy",
    "2Factor": "configured"
  }
}
```

### 6.2 Test Phone Registration
1. Open your mobile app
2. Enter a valid Indian phone number (e.g., `**********`)
3. You should receive an SMS with OTP code
4. Enter the OTP to complete registration
5. Select your preferred language

### 6.3 Verify in Supabase Dashboard
1. Go to **Table Editor** → **users**
2. You should see your new user record
3. Go to **sms_logs** to see SMS delivery logs

## 🚀 **Step 7: Production Deployment**

### 7.1 Deploy Backend Server
Deploy your backend to any cloud provider:
- **Vercel**: `vercel deploy`
- **Railway**: `railway deploy`
- **Heroku**: `git push heroku main`
- **DigitalOcean App Platform**
- **AWS/GCP/Azure**

### 7.2 Update Mobile App for Production
```env
# In mobile/.env for production
EXPO_PUBLIC_API_BASE_URL=https://your-backend-domain.com/api
```

### 7.3 Build and Deploy Mobile App
```bash
cd mobile
eas build --platform android
# or
expo build:android
```

## 🗑️ **Step 8: Remove Local Database Setup**

Since you're now using Supabase Cloud, you can remove the local database setup:

```bash
# Remove the db-migrations directory
rm -rf db-migrations/

# Update your development workflow
# You now only need:
# Terminal 1: Backend server (cd backend && npm run dev)
# Terminal 2: Mobile app (cd mobile && npm start)
```

## 📊 **Monitoring and Maintenance**

### Database Monitoring
- **Supabase Dashboard**: Monitor database performance, queries, and usage
- **Table Editor**: View and edit data directly
- **SQL Editor**: Run custom queries and analytics

### SMS Monitoring
- **2Factor Console**: Monitor SMS delivery rates and costs
- **Backend Logs**: Check `logs/combined.log` for SMS delivery logs
- **Database**: Query `sms_logs` table for delivery analytics

### Error Monitoring
- **Backend Logs**: Check `logs/error.log` for application errors
- **Supabase Logs**: Monitor database errors and slow queries

## 🔧 **Troubleshooting**

### Common Issues

1. **"Database connection failed"**
   - Check your `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`
   - Ensure your Supabase project is active (not paused)

2. **"SMS not received"**
   - Check 2Factor credentials in `.env`
   - Verify your 2Factor phone number supports SMS
   - Check 2Factor console for delivery status

3. **"Invalid phone number"**
   - Ensure phone number starts with 6-9 (Indian mobile format)
   - Use format: `**********` (without +91 prefix in the app)

4. **"Rate limit exceeded"**
   - Wait 1 hour before trying again
   - Check `otp_attempts` table in Supabase for blocked numbers

### Getting Help
- **Supabase Support**: [supabase.com/support](https://supabase.com/support)
- **2Factor Support**: [2Factor.in/help](https://2Factor.in/help)
- **Backend Logs**: Check `backend/logs/` directory

## ✅ **Setup Complete!**

Your Ozgaar platform is now running with:
- ✅ **Supabase Cloud** database (no local setup needed)
- ✅ **Node.js backend** server with JWT authentication
- ✅ **2Factor SMS** integration for OTP delivery
- ✅ **React Native** mobile app
- ✅ **Production-ready** architecture

You can now focus on building features instead of managing database infrastructure!
